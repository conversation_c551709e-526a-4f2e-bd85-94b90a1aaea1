# Booking Flow - Best Practices Guide

## 🎯 **Clean Architecture Overview**

The booking flow has been refactored to follow a clean, predictable pattern with centralized navigation logic and consistent state management.

### **Flow Diagram**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Service       │    │   Time Slot     │    │   Authentication│
│   Selection     │───▶│   Selection     │───▶│   Check         │
│   (Home Page)   │    │   (Booking)     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Booking       │    │   Review &      │    │   Consent Form  │
│   Confirmation  │◀───│   Payment       │◀───│   (if needed)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🏗️ **Key Components**

### **1. Centralized Navigation (`src/utils/navigationUtils.js`)**
- **`handlePostTimeSlotSelection()`**: Determines next step after time slot selection
- **`handlePostLoginNavigation()`**: Routes user after login based on booking context
- **`canProceedToBookingStep()`**: Validates if user can proceed to specific step
- **`getBookingRedirectRoute()`**: Gets appropriate redirect route based on current state

### **2. State Management**
- **Zustand Stores**: `authStore.js` and `bookingStore.js` for application state
- **React Query**: Server state management with automatic caching
- **Centralized Storage**: `storage.js` for consistent localStorage operations

### **3. Consent Status Management**
- **Single Source of Truth**: Auth store holds consent status for authenticated session
- **API-First**: Consent status loaded from API during login and refreshed as needed
- **No Caching**: No localStorage caching - auth store is the only source during session
- **Session-Based**: Consent status cleared when user logs out

## 📋 **Step-by-Step Flow**

### **Step 1: Service Selection**
- **Location**: Home page (`/`)
- **Component**: `MainContentWrapper` → `ServicesList`
- **Action**: User selects service and add-ons
- **Next**: Automatically switches to booking tab

### **Step 2: Time Slot Selection**
- **Location**: Home page (`/`) - booking tab
- **Component**: `BookingPage`
- **Actions**: 
  1. User selects date, employee (optional), and time slot
  2. Booking data saved to Zustand store
  3. Authentication check performed
- **Next**: Login (if not authenticated) or Consent Check

### **Step 3: Authentication (if needed)**
- **Location**: Login page (`/login`)
- **Component**: `LoginPage`
- **Actions**:
  1. User logs in
  2. Consent status checked during login
  3. Status cached for 1 hour
- **Next**: Consent Form or Review (based on consent status)

### **Step 4: Consent Check & Forms**
- **Location**: Consent form page (`/consent-form`)
- **Component**: `ConsentFormPage`
- **Actions**:
  1. Load missing forms from API
  2. Present forms sequentially
  3. Collect signatures and answers
  4. Submit each form to backend
- **Next**: Review page (after all forms completed)

### **Step 5: Review & Payment**
- **Location**: Review booking page (`/review-booking`)
- **Component**: `ReviewBookingPage`
- **Actions**:
  1. Display booking summary
  2. Collect customer information
  3. Process payment
- **Next**: Booking confirmation

### **Step 6: Confirmation**
- **Location**: Booking confirmation page (`/booking-confirmation`)
- **Component**: `BookingConfirmationPage`
- **Actions**:
  1. Display confirmation details
  2. Clear booking data
  3. Provide next steps

## 📊 **State Variables & Decision Logic**

### **Booking Data Variables**
```javascript
bookingData: {
  // Service Selection
  selectedService: { id, name, price, duration },     // Selected service object
  selectedEmployee: { id, display_name, full_name },  // Selected employee object
  selectedAddOns: [{ id, name, price }],              // Array of selected add-ons

  // Date & Time
  selectedDate: "2024-01-15",                         // Selected date (YYYY-MM-DD)
  selectedTime: "10:00",                              // Selected time (HH:MM)

  // Customer Information
  customerInfo: {
    name: "John Doe",                                 // Customer full name
    email: "<EMAIL>",                        // Customer email
    phone: "************"                            // Customer phone
  },

  // Consent & Legal
  consentData: {
    consentAgreed: true,                              // Has user agreed to consent forms
    signature: "data:image/png;base64...",            // Digital signature data
    consentTimestamp: "2024-01-15T10:00:00Z"         // When consent was signed
  },

  // Payment & Totals
  subtotal: 150.00,                                   // Service + add-ons subtotal
  tax: 12.00,                                         // Calculated tax
  total: 162.00,                                      // Final total amount

  // Flow Control
  step: "review"                                      // Current step in booking flow
}
```

### **Validation States Explained**

#### **hasConsent: true/false**
- **Purpose**: Indicates if user has signed all required consent forms
- **True when**: `authStore.hasCompletedAllForms() === true` (single source of truth)
- **False when**: User hasn't signed consent forms or auth store shows incomplete
- **Impact**: Determines if user can proceed to review/payment steps
- **Source**: Always from auth store, never from booking store

#### **hasCustomerInfo: true/false**
- **Purpose**: Indicates if all required customer information is provided
- **True when**: All three fields are filled: `name`, `email`, `phone`
- **False when**: Any required customer field is missing or empty
- **Impact**: Required for payment processing and booking confirmation

#### **canProceedToConsent: true/false**
- **Logic**: `hasService && hasEmployee && hasDate && hasTime && isAuthenticated`
- **True when**: User has completed service/time selection and is logged in
- **False when**: Missing booking selections or user not authenticated
- **Routes to**: `/consent-form` if true, `/` or `/login` if false

#### **canProceedToReview: true/false**
- **Logic**: `canProceedToConsent && hasConsent`
- **True when**: User can proceed to consent AND has signed consent forms
- **False when**: Missing booking data or consent not completed
- **Routes to**: `/review-booking` if true, `/consent-form` if false

#### **canProceedToPayment: true/false**
- **Logic**: `canProceedToReview && hasCustomerInfo`
- **True when**: User can review AND has provided all customer information
- **False when**: Missing consent or customer information
- **Routes to**: Payment processing if true, stays on review if false

### **Routing Decision Matrix**

| User State | Booking Data | Consent Status | Customer Info | Route Destination |
|------------|--------------|----------------|---------------|-------------------|
| Not Authenticated | Any | Any | Any | `/login` |
| Authenticated | Incomplete | Any | Any | `/` (home) |
| Authenticated | Complete | Not Signed | Any | `/consent-form` |
| Authenticated | Complete | Signed | Missing | `/review-booking` |
| Authenticated | Complete | Signed | Complete | `/review-booking` → Payment |

### **recommendedRoute Logic**
```javascript
export const getBookingRedirectRoute = (bookingData, user) => {
  // 1. Authentication Check
  if (!user) return '/login';

  // 2. Booking Data Validation
  if (!canProceedToBookingStep('consent', bookingData, user)) {
    return '/';  // Missing service/employee/date/time
  }

  // 3. Consent Status Check
  if (!canProceedToBookingStep('review', bookingData, user)) {
    return '/consent-form';  // Need to sign consent forms
  }

  // 4. Default to Review
  return '/review-booking';  // Ready for review/payment
};
```

## 🔧 **Implementation Details**

### **Navigation Logic**
```javascript
// Time slot selection handler
const handleTimeSlotSelect = async (time, employee) => {
  // 1. Save booking data to store
  setBookingService(service);
  setBookingEmployee(employee);
  setSelectedDateTime(date, time);

  // 2. Check authentication
  if (!isAuthenticated) {
    navigate('/login');
    return;
  }

  // 3. Use centralized navigation logic
  await handlePostTimeSlotSelection(user, navigate);
};
```

### **Simplified Consent Status Workflow**

**✅ SINGLE SOURCE OF TRUTH: Auth Store**

#### **1. Login → Load Consent Status**
```javascript
// In authService.login()
const consentResponse = await authApi.get('/business-customers/me/forms/');
useAuthStore.getState().updateConsentStatus(consentResponse.data);
```

#### **2. Time Slot Selection → Check Auth Store**
```javascript
// In navigationUtils.js - handlePostTimeSlotSelection()
export const handlePostTimeSlotSelection = async (user, navigate) => {
  // Single source of truth: check auth store
  const hasCompletedForms = authService.hasCompletedAllForms();

  if (hasCompletedForms) {
    navigate('/review-booking');
  } else {
    navigate('/consent-form');
  }
};
```

#### **3. Validation → Use Auth Store**
```javascript
// All validation functions now use auth store
export const canProceedToBookingStep = (step, bookingData, user) => {
  switch (step) {
    case 'review':
      return hasBasicBookingData && authService.hasCompletedAllForms();
  }
};
```

#### **4. Form Completion → Refresh Auth Store**
```javascript
// In ConsentFormPage.jsx after form submission
await authService.refreshConsentStatus(); // Updates auth store from API
```

#### **5. Logout → Clear Auth Store**
```javascript
// Auth store is cleared on logout, no localStorage cleanup needed
```

### **Benefits of Single Source of Truth**
- ✅ **No Cache Staleness**: No localStorage caching to get out of sync
- ✅ **No Multiple Sources**: Only auth store holds consent status
- ✅ **Session-Based**: Status cleared automatically on logout
- ✅ **API-First**: Always loaded from API, never hardcoded
- ✅ **Simpler Debugging**: Only one place to check consent status

### **Validation Logic**
```javascript
// Centralized step validation
export const canProceedToBookingStep = (step, bookingData, user) => {
  if (!user) return false;
  
  switch (step) {
    case 'consent':
      return !!(bookingData.selectedService && 
                bookingData.selectedEmployee && 
                bookingData.selectedDate && 
                bookingData.selectedTime);
    
    case 'review':
      return canProceedToBookingStep('consent', bookingData, user) && 
             bookingData.consentData.consentAgreed;
    
    // ... other cases
  }
};
```

## 🧪 **Testing Guide**

### **Test User Accounts**
- **Test User 1**: `<EMAIL>` / `Zhang0126` (forms NOT completed)
- **Test User 2**: `<EMAIL>` / `AdminPass123!` (forms completed)

### **Test Scenarios**

#### **Scenario 1: New User (Forms Not Completed)**
1. Go to home page (logged out)
2. Select service → booking tab opens
3. Select date and time slot → redirects to login
4. Login with Test User 1 → redirects to consent form
5. Complete consent forms → redirects to review page
6. Complete booking → confirmation page

#### **Scenario 2: Returning User (Forms Completed)**
1. Go to home page (logged out)
2. Select service → booking tab opens
3. Select date and time slot → redirects to login
4. Login with Test User 2 → redirects directly to review page
5. Complete booking → confirmation page

#### **Scenario 3: Already Logged In User**
1. Login first
2. Go to home page
3. Select service and time slot → routes based on consent status
4. Continue with appropriate flow

## 🚨 **Common Issues & Solutions**

### **Issue: Blank Page After Login**
- **Cause**: Missing booking data in store
- **Solution**: Check that service/time selection properly saves to Zustand store

### **Issue: Stuck in Consent Form Loop**
- **Cause**: API consent status not updating
- **Solution**: Check backend API response and cache invalidation

### **Issue: Navigation Not Working**
- **Cause**: Import errors in navigation utilities
- **Solution**: Verify all imports are correct and functions are exported

## ✅ **Best Practices Checklist**

- [ ] Always use centralized navigation functions
- [ ] Validate booking data before navigation
- [ ] Handle authentication state consistently
- [ ] Cache consent status appropriately
- [ ] Provide user feedback during transitions
- [ ] Log navigation decisions for debugging
- [ ] Handle API errors gracefully
- [ ] Clear booking data after completion

## 🐛 **Debugging Guide**

### **Debug Mode**
Enable debug mode for detailed logging:
```javascript
// In browser console
localStorage.setItem('booking_debug', 'true');
// Refresh page to see detailed logs
```

### **Debug State Variables**
Use `validateBookingFlow()` in console to see all state variables:

```javascript
// Example output from validateBookingFlow()
{
  authentication: {
    isAuthenticated: true,
    hasUser: true,
    userEmail: "<EMAIL>"
  },
  bookingData: {
    hasService: true,        // ✅ Service selected
    hasEmployee: true,       // ✅ Employee selected
    hasDate: true,          // ✅ Date selected
    hasTime: true,          // ✅ Time selected
    hasConsent: true,       // ⚠️  This might be wrong!
    hasCustomerInfo: false  // ❌ Missing customer info
  },
  flowValidation: {
    canProceedToConsent: true,     // Can go to consent form
    canProceedToReview: true,      // Can go to review page
    canProceedToPayment: false,    // Cannot proceed to payment
    recommendedRoute: "/review-booking"
  },
  issues: ["Missing customer information"]
}
```

### **Common Issues**

#### **Issue: 401 Authentication Errors**
**Symptoms**: API calls return 401 "Authentication credentials were not provided"

**Debug Steps**:
```javascript
// 1. Check token validity and auth store consistency
testAuthStoreConsent();

// 2. Look for specific issues:
// - Token expired
// - Token format invalid
// - Auth store vs localStorage inconsistency
```

**Root Causes**:
- **Expired Token**: JWT token has expired but frontend still thinks user is logged in
- **Invalid Token**: Token format is corrupted
- **Auth Inconsistency**: Auth store says authenticated but no valid token

**Solutions**:
```javascript
// Fix authentication inconsistencies automatically
await fixAuthInconsistencies();

// Manual fix if needed:
// Clear expired/invalid tokens
localStorage.removeItem('auth_token');
localStorage.removeItem('user_data');
useAuthStore.getState().logout();

// User will need to log in again
window.location.href = '/login';
```

#### **Issue: hasConsent shows true but user needs consent forms**
**Symptoms**: User goes directly to review-booking instead of consent-form

**Debug Steps**:
```javascript
// 1. Check auth store consent status (single source of truth)
const authStore = useAuthStore.getState();
const consentStatus = authStore.getConsentStatus();
console.log('📝 Auth store consent:', consentStatus);
console.log('✅ Has completed forms:', authStore.hasCompletedAllForms());

// 2. Check API consent status
import { checkUserConsentStatus } from './src/api/consentApi';
checkUserConsentStatus().then(api => {
    console.log('🌐 API consent:', api);
    console.log('❓ Auth store vs API match:',
        authStore.hasCompletedAllForms() === api.all_forms_completed);
});
```

**Root Causes**:
- **Stale Auth Store**: Auth store not updated after form submission
- **API Sync Issue**: Auth store out of sync with API
- **Login Issue**: Consent status not loaded properly during login

**Solutions**:
```javascript
// Refresh consent status from API
await authService.refreshConsentStatus();

// Check if auth store is now correct
console.log('Updated status:', useAuthStore.getState().hasCompletedAllForms());

// Force navigation to consent form if still incorrect
if (!useAuthStore.getState().hasCompletedAllForms()) {
    window.location.href = '/consent-form';
}
```

#### **400 Error on Login**
- Check request format in browser console (📤 logs)
- Verify field names match backend expectations
- Check CORS headers and preflight requests

#### **Consent Form Loop**
- Check API response for consent status
- Verify cache invalidation is working
- Look for 📝 logs showing consent status checks

#### **Missing Booking Data**
- Use `debugBookingState()` in console to check current state
- Verify Zustand store persistence is working
- Check for navigation state loss

### **Debug Console Commands**
```javascript
// Quick state check
validateBookingFlow();

// Detailed booking state
debugBookingState();

// Check all three consent sources
const userId = useAuthStore.getState().user?.id;
console.log('🌐 API:', await checkUserConsentStatus());
console.log('💾 Cache:', authService.getStoredConsentStatus(userId));
console.log('📋 Store:', useBookingStore.getState().bookingData.consentData);

// Force consent form workflow
localStorage.removeItem(`consent_status_${userId}`);
useBookingStore.getState().setConsentData({ consentAgreed: false });
window.location.href = '/consent-form';

// Enable/disable debug mode
setDebugMode(true);  // Enable detailed logging
setDebugMode(false); // Disable
```

### **Quick Fix for Your Issue**
Based on your debug output showing `hasConsent: true` but user probably needs consent:

```javascript
// 1. Check auth store (single source of truth)
const authStore = useAuthStore.getState();
console.log('Auth store consent status:', authStore.getConsentStatus());
console.log('Has completed all forms:', authStore.hasCompletedAllForms());

// 2. Refresh from API to ensure accuracy
await authService.refreshConsentStatus();
console.log('After refresh:', authStore.hasCompletedAllForms());

// 3. If API shows forms needed, navigate to consent form
checkUserConsentStatus().then(api => {
    console.log('API consent status:', api);
    if (!api.all_forms_completed) {
        console.log('❌ API says forms needed - navigating to consent form');
        window.location.href = '/consent-form';
    }
});
```

## 🔮 **Future Enhancements**

1. **URL State Sync**: Maintain booking state in URL for shareable links
2. **Offline Support**: Handle network failures gracefully
3. **Real-time Updates**: WebSocket integration for live availability
4. **Analytics**: Track user flow and drop-off points
5. **A/B Testing**: Test different flow variations
