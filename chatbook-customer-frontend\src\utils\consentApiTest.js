/**
 * Test utility to verify consent API endpoint
 * Use this in browser console to test the consent API
 */

import { authService } from '../features/auth/services/authApi';
import { checkUserConsentStatus, resetConsentStatusCircuitBreaker } from '../api/consentApi';

/**
 * Test the consent API endpoint that's called during login
 */
export const testConsentAPI = async () => {
  console.group('🧪 Testing Consent API Endpoints');
  
  try {
    // Test 1: Check the endpoint used during login
    console.log('📡 Testing login consent endpoint: /api/v1/business-customers/me/forms/');
    const loginConsentResponse = await authService.refreshConsentStatus();
    console.log('✅ Login consent endpoint response:', loginConsentResponse);
    
    // Test 2: Check the endpoint used by consent form page
    console.log('📡 Testing consent form endpoint (from consentApi.js)');
    const consentFormResponse = await checkUserConsentStatus();
    console.log('✅ Consent form endpoint response:', consentFormResponse);
    
    // Test 3: Compare responses
    console.log('🔍 Comparing responses:');
    console.log('Login endpoint all_forms_completed:', loginConsentResponse?.all_forms_completed);
    console.log('Consent form endpoint all_forms_completed:', consentFormResponse?.all_forms_completed);
    
    const match = loginConsentResponse?.all_forms_completed === consentFormResponse?.all_forms_completed;
    console.log('✅ Responses match:', match);
    
    if (!match) {
      console.warn('⚠️ API endpoints returning different results!');
      console.log('Login response:', loginConsentResponse);
      console.log('Consent form response:', consentFormResponse);
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error);
    console.log('Error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url
    });
  }
  
  console.groupEnd();
};

/**
 * Test auth store consent status and token validity
 */
export const testAuthStoreConsent = () => {
  console.group('🧪 Testing Auth Store Consent Status');

  try {
    const { useAuthStore } = require('../stores/authStore');
    const authStore = useAuthStore.getState();

    // Check token validity
    const token = localStorage.getItem('auth_token');
    console.log('🔒 Token status:');
    console.log('- Token exists:', !!token);

    if (token) {
      try {
        const parts = token.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]));
          const currentTime = Math.floor(Date.now() / 1000);
          const isExpired = payload.exp && payload.exp < currentTime;

          console.log('- Token format: Valid JWT');
          console.log('- Expires at:', payload.exp ? new Date(payload.exp * 1000) : 'No expiration');
          console.log('- Current time:', new Date());
          console.log('- Is expired:', isExpired);
          console.log('- Time left:', payload.exp ? `${Math.floor((payload.exp - currentTime) / 60)} minutes` : 'N/A');

          if (isExpired) {
            console.warn('⚠️ TOKEN IS EXPIRED! This explains the 401 errors.');
          }
        } else {
          console.log('- Token format: Invalid (not JWT)');
        }
      } catch (error) {
        console.log('- Token format: Invalid (parse error)');
      }
    }

    console.log('📊 Auth store state:');
    console.log('- User:', authStore.user?.email || 'Not logged in');
    console.log('- Is authenticated:', authStore.isAuthenticated);
    console.log('- Consent status:', authStore.getConsentStatus());
    console.log('- Has completed all forms:', authStore.hasCompletedAllForms());

    const consentStatus = authStore.getConsentStatus();
    if (consentStatus) {
      console.log('📝 Consent details:');
      console.log('- All forms completed:', consentStatus.all_forms_completed);
      console.log('- Missing forms:', consentStatus.missing_forms);
      console.log('- Checked at:', consentStatus.checked_at);
    } else {
      console.warn('⚠️ No consent status in auth store');
    }

    // Check for inconsistencies
    if (authStore.isAuthenticated && !token) {
      console.error('❌ INCONSISTENCY: Auth store says authenticated but no token found!');
    }

    if (!authStore.isAuthenticated && token) {
      console.warn('⚠️ INCONSISTENCY: Token exists but auth store says not authenticated');
    }

  } catch (error) {
    console.error('❌ Auth store test failed:', error);
  }

  console.groupEnd();
};

/**
 * Fix authentication inconsistencies
 */
export const fixAuthInconsistencies = async () => {
  console.group('🔧 Fixing Authentication Inconsistencies');

  try {
    const { useAuthStore } = require('../stores/authStore');
    const authStore = useAuthStore.getState();
    const token = localStorage.getItem('auth_token');

    // Check for expired token
    if (token) {
      try {
        const parts = token.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]));
          const currentTime = Math.floor(Date.now() / 1000);
          const isExpired = payload.exp && payload.exp < currentTime;

          if (isExpired) {
            console.log('🧹 Clearing expired token...');
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');

            // Update auth store
            authStore.logout();

            console.log('✅ Expired token cleared, please log in again');
            return { fixed: true, action: 'cleared_expired_token' };
          }
        }
      } catch (error) {
        console.log('🧹 Clearing invalid token...');
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        authStore.logout();

        console.log('✅ Invalid token cleared, please log in again');
        return { fixed: true, action: 'cleared_invalid_token' };
      }
    }

    // Check for auth store inconsistencies
    if (authStore.isAuthenticated && !token) {
      console.log('🔄 Resetting auth store (no token)...');
      authStore.logout();
      return { fixed: true, action: 'reset_auth_store' };
    }

    console.log('✅ No authentication inconsistencies found');
    return { fixed: false, action: 'none' };

  } catch (error) {
    console.error('❌ Failed to fix auth inconsistencies:', error);
    return { fixed: false, error: error.message };
  }

  console.groupEnd();
};

/**
 * Test login page rendering
 */
export const testLoginPageRendering = () => {
  console.group('🧪 Testing Login Page Rendering');

  console.log('Current URL:', window.location.href);
  console.log('Current pathname:', window.location.pathname);
  console.log('Current search:', window.location.search);

  // Check if we're on login page
  if (window.location.pathname === '/login') {
    console.log('✅ On login page');

    // Check if page content exists
    const loginContent = document.querySelector('.min-h-screen');
    console.log('Login content found:', !!loginContent);

    if (loginContent) {
      console.log('✅ Login page content exists');
      console.log('Content HTML:', loginContent.innerHTML.substring(0, 200) + '...');
    } else {
      console.log('❌ Login page content NOT found');
      console.log('Page body:', document.body.innerHTML.substring(0, 500) + '...');
    }
  } else {
    console.log('❌ Not on login page, current path:', window.location.pathname);
  }

  // Check React app mounting
  const reactRoot = document.getElementById('root');
  console.log('React root found:', !!reactRoot);
  if (reactRoot) {
    console.log('React root has content:', reactRoot.innerHTML.length > 0);
  }

  console.groupEnd();
};

/**
 * Emergency stop for infinite loops
 */
export const emergencyStop = () => {
  console.group('🚨 EMERGENCY STOP - Breaking Navigation Loops');

  // Clear all auth data
  localStorage.removeItem('auth_token');
  localStorage.removeItem('user_data');

  // Clear session flags
  sessionStorage.removeItem('auth_logout_triggered');

  // Reset circuit breakers
  resetConsentStatusCircuitBreaker();

  // Force logout in auth store
  try {
    const { useAuthStore } = require('../stores/authStore');
    useAuthStore.getState().logout();
  } catch (error) {
    console.warn('Could not access auth store');
  }

  console.log('🛑 Emergency stop complete - redirecting to home page');
  window.location.href = '/';

  console.groupEnd();
};

/**
 * Reset circuit breakers and test state
 */
export const resetTestState = () => {
  console.group('🔄 Resetting Test State');

  // Reset consent API circuit breaker
  resetConsentStatusCircuitBreaker();

  // Clear session flags that prevent loops
  sessionStorage.removeItem('auth_logout_triggered');

  // Reset any page-level state flags
  if (typeof window !== 'undefined') {
    console.log('🔄 Cleared session flags and reset circuit breakers');
    console.log('🔄 You may need to refresh the page to reset React state');
  }

  console.log('✅ Test state reset complete');
  console.groupEnd();
};

/**
 * Test the navigation orchestration flow
 */
export const testNavigationOrchestration = () => {
  console.group('🧪 Testing Navigation Orchestration');

  console.log('✅ Correct Flow (NO API calls in navigation):');
  console.log('1. User clicks time slot');
  console.log('2. BookingPage checks: isAuthenticated && user');
  console.log('3. If not authenticated → redirect to /login');
  console.log('4. If authenticated → call handlePostTimeSlotSelection()');
  console.log('5. Navigation function checks auth store (NO API calls)');
  console.log('6. If no consent status in store → redirect to /login for refresh');
  console.log('7. If consent status exists → navigate based on status');
  console.log('8. Only AFTER navigation → page makes API calls if needed');

  console.log('🚫 What should NOT happen:');
  console.log('- Navigation function making API calls');
  console.log('- Going to consent form page without consent status');
  console.log('- API calls during navigation orchestration');

  console.log('🔍 Expected behavior:');
  console.log('- Time slot click → immediate navigation decision');
  console.log('- No API calls until user reaches final page');
  console.log('- Consent form page only loads if user should be there');

  console.groupEnd();
};

/**
 * Test the fixes for infinite API calls and 401 errors
 */
export const testInfiniteCallFixes = () => {
  console.group('🧪 Testing Infinite API Call Fixes');

  console.log('✅ Fixes implemented:');
  console.log('1. Navigation orchestration - checks auth store BEFORE making API calls');
  console.log('2. Circuit breaker in consentApi.js - prevents multiple simultaneous calls');
  console.log('3. hasAttemptedLoad flag in ConsentFormPage - prevents useEffect re-runs');
  console.log('4. 5-second cooldown after errors - prevents immediate retries');
  console.log('5. 401 error handling - auto logout and redirect (one time only)');
  console.log('6. Global 401 interceptor in useApi.js - backup prevention');
  console.log('7. Error state display - shows error instead of infinite loading');
  console.log('8. Consolidated useEffects - removed duplicate API triggers');
  console.log('9. Navigation loop prevention - sessionStorage flags prevent bouncing');
  console.log('10. Login page loop breaking - redirects to home if came from failed consent');

  console.log('🔍 To test:');
  console.log('1. Should see only ONE API call attempt (if any)');
  console.log('2. Navigation should happen BEFORE API calls');
  console.log('3. If 401 error: immediate redirect to login (no retries)');
  console.log('4. If other error: error message shown (no retries)');
  console.log('5. No page flashing or infinite loading');

  console.log('🛠️ Debug commands:');
  console.log('- testNavigationOrchestration() - understand the flow');
  console.log('- resetTestState() - reset circuit breakers');
  console.log('- testAuthStoreConsent() - check auth state');

  console.groupEnd();
};

/**
 * Complete consent flow test
 */
export const testCompleteConsentFlow = async () => {
  console.group('🧪 Testing Complete Consent Flow');

  try {
    // Step 1: Test infinite call fixes
    console.log('Step 1: Checking infinite call fixes...');
    testInfiniteCallFixes();

    // Step 2: Fix any auth inconsistencies first
    console.log('Step 2: Checking and fixing auth inconsistencies...');
    const fixResult = await fixAuthInconsistencies();

    if (fixResult.fixed) {
      console.log('⚠️ Authentication issues were found and fixed. Please log in again and retry.');
      console.groupEnd();
      return;
    }

    // Step 3: Check auth store
    console.log('Step 3: Checking auth store...');
    testAuthStoreConsent();

    // Step 4: Test API endpoints
    console.log('Step 4: Testing API endpoints...');
    await testConsentAPI();

    // Step 5: Test navigation logic
    console.log('Step 5: Testing navigation logic...');
    const { useAuthStore } = require('../stores/authStore');
    const authStore = useAuthStore.getState();
    const hasCompleted = authStore.hasCompletedAllForms();

    console.log('🎯 Navigation decision:');
    console.log('- Has completed all forms:', hasCompleted);
    console.log('- Should navigate to:', hasCompleted ? '/review-booking' : '/consent-form');

  } catch (error) {
    console.error('❌ Complete flow test failed:', error);
  }

  console.groupEnd();
};

// Make functions available globally in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.testConsentAPI = testConsentAPI;
  window.testAuthStoreConsent = testAuthStoreConsent;
  window.testCompleteConsentFlow = testCompleteConsentFlow;
  window.fixAuthInconsistencies = fixAuthInconsistencies;
  window.testInfiniteCallFixes = testInfiniteCallFixes;
  window.testNavigationOrchestration = testNavigationOrchestration;
  window.testLoginPageRendering = testLoginPageRendering;
  window.resetTestState = resetTestState;
  window.emergencyStop = emergencyStop;

  console.log('🔧 Consent API test functions available:');
  console.log('- testNavigationOrchestration() 🎯 - UNDERSTAND THE FLOW');
  console.log('- testLoginPageRendering() 🎨 - CHECK LOGIN PAGE');
  console.log('- testConsentAPI()');
  console.log('- testAuthStoreConsent()');
  console.log('- testCompleteConsentFlow()');
  console.log('- fixAuthInconsistencies()');
  console.log('- testInfiniteCallFixes()');
  console.log('- resetTestState()');
  console.log('- emergencyStop() 🚨 - BREAKS INFINITE LOOPS');
}

export default {
  testConsentAPI,
  testAuthStoreConsent,
  testCompleteConsentFlow,
  fixAuthInconsistencies,
  testInfiniteCallFixes,
  testNavigationOrchestration,
  testLoginPageRendering,
  resetTestState,
  emergencyStop
};
