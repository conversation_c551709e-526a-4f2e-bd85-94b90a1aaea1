from rest_framework import viewsets, permissions, status, mixins
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from business.models import BusinessCustomer, Business, Location, OnlineBookingRules, StylistLevel
from customers.models import CustomerProfile, CustomerTag
from appointments.models import Appointment
from services.models import LashServiceSuggestionRule, AddonSuggestionRule
from forms.models import BusinessRequiredForm, FormSubmission
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, date
import logging
from .serializers import (
    BusinessCustomerSerializer,
    BusinessCustomerUpdateSerializer,
    CustomerTagAssignmentSerializer,
    BusinessSerializer,
    LocationSerializer,
    OnlineBookingRulesSerializer,
    StylistLevelSerializer,
    BusinessCustomerFlatSerializer
)

logger = logging.getLogger(__name__)
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.contrib.auth import get_user_model
from api.permissions import IsAdminUserOrReadOnly
from employees.models import Employee

User = get_user_model()


class BusinessPermission(permissions.BasePermission):
    """
    Permission to only allow business owners or staff to access their business data
    """
    def has_permission(self, request, view):
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return False
        
        # Allow access if user is staff or superuser
        if request.user.is_staff or request.user.is_superuser:
            return True
            
        # Check business permissions
        business_id = request.query_params.get('business_id')
        if not business_id:
            return False
            
        # Check if user is a member of the business
        return request.user.business_memberships.filter(
            business_id=business_id,
            is_active=True
        ).exists()

    def has_object_permission(self, request, view, obj):
        # Allow access if user is staff or superuser
        if request.user.is_staff or request.user.is_superuser:
            return True
            
        # Check if user is a member of the business
        business_id = obj.business.id
        return request.user.business_memberships.filter(
            business_id=business_id,
            is_active=True
        ).exists()


class BusinessCustomerViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows business customers to be viewed or edited.
    
    Requires a business_id query parameter for all operations.
    """
    serializer_class = BusinessCustomerSerializer
    permission_classes = [permissions.IsAuthenticated, BusinessPermission]
    
    def get_queryset(self):
        """
        This view returns all business customers for a specific business.
        Supports filtering with the 'q' query parameter for search.
        """
        business_id = self.request.query_params.get('business_id')
        queryset = BusinessCustomer.objects.filter(business_id=business_id)
        
        # Handle search query parameter if present
        search_term = self.request.query_params.get('q', '')
        if search_term:
            # Find users matching the search term
            users = User.objects.filter(
                Q(email__icontains=search_term) |
                Q(phone__icontains=search_term) |
                Q(first_name__icontains=search_term) |
                Q(last_name__icontains=search_term)
            )
            
            # Find customer profiles for these users
            customer_profiles = CustomerProfile.objects.filter(user__in=users)
            
            # Filter business customers that match
            queryset = queryset.filter(
                customer__in=customer_profiles
            )
            
        return queryset
    
    def get_serializer_class(self):
        """
        Return different serializers for different actions
        """
        if self.action in ['update', 'partial_update']:
            return BusinessCustomerUpdateSerializer
        return BusinessCustomerSerializer
    
    def perform_create(self, serializer):
        """
        Set the business when creating a new business customer
        """
        business_id = self.request.query_params.get('business_id')
        business = get_object_or_404(Business, id=business_id)
        serializer.save(business=business)


class BusinessViewSet(viewsets.ModelViewSet):
    """
    API endpoint for business information
    """
    queryset = Business.objects.all()
    serializer_class = BusinessSerializer
    permission_classes = [IsAdminUserOrReadOnly]

    @action(detail=True, methods=['get'], url_path='appointments/available-times')
    def appointments_available_times(self, request, pk=None):
        """
        Return available time slots for a given business, date, and service with automatic smart booking rules.

        Required path parameters:
        - pk: ID of the business (from URL)

        Required query parameters:
        - date: YYYY-MM-DD format
        - service_id: ID of the requested service

        Optional query parameters:
        - employee_id: ID of a specific employee (if not provided, checks all employees)

        Returns:
        A simplified response with applied rules and availability grouped by employee
        """
        from ..appointments.views import BusinessAppointmentViewSet

        # Create an instance of the existing BusinessAppointmentViewSet
        appointment_viewset = BusinessAppointmentViewSet()
        appointment_viewset.request = request
        appointment_viewset.format_kwarg = getattr(self, 'format_kwarg', None)
        appointment_viewset.action = 'available_times'

        # Call the existing available_times method with business_id from URL
        return appointment_viewset.available_times(request, business_id=pk)


class LocationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for business locations
    """
    queryset = Location.objects.all()
    serializer_class = LocationSerializer
    permission_classes = [IsAdminUserOrReadOnly]
    
    def get_queryset(self):
        """Filter locations by business"""
        queryset = Location.objects.all()
        business_id = self.request.query_params.get('business_id', None)
        if business_id is not None:
            queryset = queryset.filter(business_id=business_id)
        return queryset


class OnlineBookingRulesViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for online booking rules
    """
    queryset = OnlineBookingRules.objects.all()
    serializer_class = OnlineBookingRulesSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        """Filter booking rules by business"""
        queryset = OnlineBookingRules.objects.all()
        business_id = self.request.query_params.get('business_id', None)
        if business_id is not None:
            queryset = queryset.filter(business_id=business_id)
        return queryset
    
    @action(detail=False, methods=['get'], url_path='by-business/(?P<business_id>[^/.]+)')
    def by_business(self, request, business_id=None):
        """
        Get booking rules for a specific business
        """
        booking_rules = get_object_or_404(OnlineBookingRules, business_id=business_id)
        serializer = self.get_serializer(booking_rules)
        return Response(serializer.data)


class StylistLevelViewSet(viewsets.ModelViewSet):
    """
    API endpoint for stylist levels
    """
    queryset = StylistLevel.objects.all()
    serializer_class = StylistLevelSerializer
    permission_classes = [IsAdminUserOrReadOnly]
    
    def get_queryset(self):
        """Filter stylist levels by business"""
        queryset = StylistLevel.objects.all()
        business_id = self.request.query_params.get('business_id', None)
        if business_id is not None:
            queryset = queryset.filter(business_id=business_id)
        return queryset


class BusinessCustomerCurrentUserView(APIView):
    """
    API endpoint for managing business customers for the current user's business.
    Uses the logged-in user's business ID rather than requiring it as a parameter.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """
        List all customers for the current user's business with iOS compatibility and sparse fieldsets support
        """
        try:
            # Import the iOS serializer
            from .serializers import BusinessCustomerIOSSerializer

            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            business_id = employee.business.id

            # Get all business customers for this business
            queryset = BusinessCustomer.objects.filter(business_id=business_id).order_by('-created_at')

            # Handle search query parameter if present
            # Use query_params for DRF requests, GET for regular Django requests
            query_params = getattr(request, 'query_params', request.GET)
            search_term = query_params.get('q', '')
            if search_term:
                # Find users matching the search term
                users = User.objects.filter(
                    Q(email__icontains=search_term) |
                    Q(phone__icontains=search_term) |
                    Q(first_name__icontains=search_term) |
                    Q(last_name__icontains=search_term)
                )

                # Find customer profiles for these users
                customer_profiles = CustomerProfile.objects.filter(user__in=users)

                # Filter business customers that match
                queryset = queryset.filter(
                    customer__in=customer_profiles
                )

            # Handle sparse fieldsets - get requested fields from query parameter
            fields_param = query_params.get('fields', None)
            fields = None
            if fields_param:
                # Split comma-separated fields and strip whitespace
                fields = [field.strip() for field in fields_param.split(',') if field.strip()]

            # Paginate the results
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = BusinessCustomerIOSSerializer(page, many=True, fields=fields)
                return self.get_paginated_response(serializer.data)

            # Serialize and return all results if pagination is not configured
            serializer = BusinessCustomerIOSSerializer(queryset, many=True, fields=fields)
            return Response(serializer.data)

        except Exception as e:
            return Response({
                'error': 'An error occurred while fetching business customers',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request, *args, **kwargs):
        """
        Create a new business customer for the current user's business
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            business = employee.business
            
            # Create serializer with the request data
            serializer = BusinessCustomerSerializer(data=request.data)
            if serializer.is_valid():
                # Set the business before saving
                serializer.save(business=business)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            return Response({
                'error': 'An error occurred while creating business customer',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    # Add pagination methods
    @property
    def paginator(self):
        """
        The paginator instance associated with the view, or `None`.
        """
        if not hasattr(self, '_paginator'):
            from rest_framework.pagination import PageNumberPagination
            self._paginator = PageNumberPagination()
        return self._paginator

    def paginate_queryset(self, queryset):
        """
        Return a single page of results, or `None` if pagination is disabled.
        """
        if self.paginator is None:
            return None
        try:
            return self.paginator.paginate_queryset(queryset, self.request, view=self)
        except AttributeError:
            # If request doesn't have query_params (e.g., in tests), skip pagination
            return None

    def get_paginated_response(self, data):
        """
        Return a paginated style `Response` object for the given output data.
        """
        assert self.paginator is not None
        return self.paginator.get_paginated_response(data)


class BusinessCustomerDetailCurrentUserView(APIView):
    """
    API endpoint for managing a specific business customer for the current user's business.
    Uses the logged-in user's business ID rather than requiring it as a parameter.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, pk, *args, **kwargs):
        """
        Retrieve a specific business customer with iOS compatibility and sparse fieldsets support
        """
        try:
            # Import the iOS serializer
            from .serializers import BusinessCustomerIOSSerializer

            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            business_id = employee.business.id

            # Get the specific business customer
            business_customer = get_object_or_404(BusinessCustomer, id=pk, business_id=business_id)

            # Handle sparse fieldsets - get requested fields from query parameter
            query_params = getattr(request, 'query_params', request.GET)
            fields_param = query_params.get('fields', None)
            fields = None
            if fields_param:
                # Split comma-separated fields and strip whitespace
                fields = [field.strip() for field in fields_param.split(',') if field.strip()]

            # Serialize and return the business customer
            serializer = BusinessCustomerIOSSerializer(business_customer, fields=fields)
            return Response(serializer.data)

        except BusinessCustomer.DoesNotExist:
            return Response({
                'error': 'Business customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'An error occurred while fetching business customer',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request, pk, *args, **kwargs):
        """
        Update a specific business customer
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            business_id = employee.business.id
            
            # Get the specific business customer
            business_customer = get_object_or_404(BusinessCustomer, id=pk, business_id=business_id)
            
            # Create serializer with the request data and instance
            serializer = BusinessCustomerUpdateSerializer(business_customer, data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        except BusinessCustomer.DoesNotExist:
            return Response({
                'error': 'Business customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'An error occurred while updating business customer',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def patch(self, request, pk, *args, **kwargs):
        """
        Partially update a specific business customer
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            business_id = employee.business.id
            
            # Get the specific business customer
            business_customer = get_object_or_404(BusinessCustomer, id=pk, business_id=business_id)
            
            # Create serializer with the request data and instance
            serializer = BusinessCustomerUpdateSerializer(business_customer, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        except BusinessCustomer.DoesNotExist:
            return Response({
                'error': 'Business customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'An error occurred while updating business customer',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, pk, *args, **kwargs):
        """
        Delete a specific business customer
        """
        try:
            # Get the employee profile for the current user
            employee = get_object_or_404(Employee, user=request.user)
            business_id = employee.business.id
            
            # Get the specific business customer
            business_customer = get_object_or_404(BusinessCustomer, id=pk, business_id=business_id)
            
            # Delete the business customer
            business_customer.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
            
        except BusinessCustomer.DoesNotExist:
            return Response({
                'error': 'Business customer not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'An error occurred while deleting business customer',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BusinessCustomerServiceSuggestionsView(APIView):
    """
    API endpoint for getting service suggestions for a specific business customer.
    Uses the logged-in user's business ID and validates the customer belongs to that business.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, customer_id, *args, **kwargs):
        """
        Get service suggestions for a customer based on their appointment history.

        Query Parameters:
        - requested_date (optional): Date for the appointment in YYYY-MM-DD format (today or future)
                                   If not provided, defaults to today

        Returns suggestions based on:
        1. Customer's last completed appointment
        2. Lash service suggestion rules
        3. Addon suggestion rules (always included)
        4. Employee availability for suggested services
        """
        try:
            # Get the employee profile for the current user
            from employees.models import Employee
            from django.shortcuts import get_object_or_404

            employee = get_object_or_404(Employee, user=request.user)
            business = employee.business

            # Handle optional requested_date parameter
            requested_date_str = request.query_params.get('requested_date')
            requested_date_provided = bool(requested_date_str)

            if requested_date_str:
                try:
                    requested_date = datetime.strptime(requested_date_str, '%Y-%m-%d').date()
                except ValueError:
                    return Response({
                        'error': 'Invalid date format. Use YYYY-MM-DD'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Validate date is not in the past (today or future is allowed)
                today = timezone.now().date()
                if requested_date < today:
                    return Response({
                        'error': 'Date cannot be in the past. Use today or future date.'
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                # Default to today if not provided
                requested_date = timezone.now().date()

            # Get and validate customer belongs to this business
            customer = get_object_or_404(BusinessCustomer, id=customer_id, business=business)

            # Get customer's last completed appointment
            last_appointment = Appointment.objects.filter(
                customer=customer,
                employee__business=business,
                status='completed'
            ).order_by('-start_time').first()

            if not last_appointment:
                response_data = {
                    'customer_id': int(customer_id),
                    'last_appointment': None,
                    'suggestions': []
                }
                if requested_date_provided:
                    response_data['requested_date'] = requested_date_str
                return Response(response_data)

            # Calculate days since last appointment to today (for determining which services to show)
            days_since_today = (timezone.now().date() - last_appointment.start_time.date()).days
            logger.info(f"Days since last appointment to today: {days_since_today} (today: {timezone.now().date()}, last_appointment: {last_appointment.start_time.date()})")

            # For addon calculations, we'll calculate days_since_requested_date for each specific date being checked

            # Get services and add-ons from last appointment with style groups
            last_services = []
            last_add_ons = []
            last_style_groups = set()

            for app_service in last_appointment.appointment_services.all():
                service = app_service.service
                last_services.append({
                    'id': service.id,
                    'short_name': service.short_name or service.name,
                    'style_group': service.style_group.name if service.style_group else None,
                    'color': service.color
                })
                if service.style_group:
                    last_style_groups.add(service.style_group)

            for app_addon in last_appointment.appointment_add_ons.all():
                addon = app_addon.add_on
                last_add_ons.append({
                    'id': addon.id,
                    'short_name': addon.short_name or addon.name,
                    'color': addon.color
                })

            # Helper function to get employee availability using the same logic as the working endpoint
            def get_employee_availability(service, addon_ids, target_date):
                """Get availability for employees who can perform this service and add-ons"""
                logger.info(f"Getting availability for service {service.id} ({service.short_name}) with add-ons {addon_ids} on date {target_date}")
                print(f"DEBUG: Getting availability for service {service.id} ({service.short_name}) with add-ons {addon_ids} on date {target_date}")

                try:
                    # Use the new BusinessAppointmentViewSet for consistent availability calculation
                    from ..appointments.views import BusinessAppointmentViewSet

                    # Create an instance and call the available_times method directly
                    business_appointment_viewset = BusinessAppointmentViewSet()

                    # Create a mock request object with the required parameters
                    class MockRequest:
                        def __init__(self):
                            self.query_params = {
                                'date': target_date.strftime('%Y-%m-%d'),
                                'service_id': str(service.id)
                                # No group_by parameter needed - always groups by employee
                            }
                            # Mock other required request attributes
                            self.user = None
                            self.method = 'GET'
                            self.META = {}

                    mock_request = MockRequest()

                    # Call the new business-specific available_times method
                    response = business_appointment_viewset.available_times(mock_request, business_id=business.id)

                    logger.info(f"Available times response status: {response.status_code}")

                    if response.status_code == 200:
                        response_data = response.data
                        logger.info(f"Raw availability response keys: {list(response_data.keys())}")

                        # Extract availability from the simplified response format
                        # Response format: {"applied_rules": [...], "availability": {"Employee Name": [slots]}}
                        availability_data = response_data.get('availability', {})

                        # Convert to the expected format for service suggestions
                        formatted_availability = []
                        for employee_name, slots in availability_data.items():
                            # Find employee by name to get ID
                            name_parts = employee_name.split()
                            if len(name_parts) >= 2:
                                first_name, last_name = name_parts[0], name_parts[-1]
                                try:
                                    emp = Employee.objects.get(
                                        business=business,
                                        user__first_name=first_name,
                                        user__last_name=last_name
                                    )

                                    # Slots are already in ISO format from the availability calculation
                                    if slots:  # Only include if there are available slots
                                        formatted_availability.append({
                                            'employee_id': emp.id,
                                            'employee_name': employee_name,
                                            'slots': slots  # Already in ISO format
                                        })

                                except Employee.DoesNotExist:
                                    logger.warning(f"Could not find employee with name {employee_name}")
                                    continue

                        logger.info(f"Formatted availability data: {len(formatted_availability)} employees")
                        return formatted_availability
                    else:
                        logger.error(f"Available times endpoint returned error: {response.status_code}")
                        return []

                except Exception as e:
                    logger.exception(f"Error in availability calculation: {str(e)}")
                    return []

            # Find suggested services and create suggestions array
            suggestions = []
            suggestion_id = 1

            # Find service suggestions based on lash service suggestion rules
            for style_group in last_style_groups:
                if requested_date_provided:
                    # When requested_date is provided, calculate days since for that specific date
                    days_since_requested_date = (requested_date - last_appointment.start_time.date()).days
                    # Find the single best matching rule for the requested date
                    matching_rule = LashServiceSuggestionRule.objects.filter(
                        business=business,
                        last_service_style_group=style_group,
                        min_days_since__lte=days_since_requested_date,
                        max_days_since__gt=days_since_requested_date,
                        is_active=True
                    ).first()

                    if matching_rule:
                        suggested_service = {
                            'id': matching_rule.next_service.id,
                            'short_name': matching_rule.next_service.short_name or matching_rule.next_service.name,
                            'style_group': matching_rule.next_service.style_group.name if matching_rule.next_service.style_group else None,
                            'color': matching_rule.next_service.color
                        }

                        # Create initial suggestion without availability (will be updated later)
                        suggestions.append({
                            'suggestion_id': suggestion_id,
                            'suggested_appointment_services': suggested_service,
                            'suggested_appointment_add_ons': [],  # Will be populated below
                            'availability': []  # Will be calculated after add-ons are determined
                        })
                        suggestion_id += 1
                        break
                else:
                    # When no requested_date is provided, find all future eligible options
                    # Show all options where the window hasn't closed yet (days_since_today < max_days_since)
                    matching_rules = LashServiceSuggestionRule.objects.filter(
                        business=business,
                        last_service_style_group=style_group,
                        max_days_since__gt=days_since_today,  # Window hasn't closed yet (future eligible)
                        is_active=True
                    ).order_by('min_days_since')  # Order by preference (2-week, 3-week, 4-week, fullset)

                    if matching_rules.exists():
                        # Create a suggestion for each eligible service
                        for rule in matching_rules:
                            suggested_service = {
                                'id': rule.next_service.id,
                                'short_name': rule.next_service.short_name or rule.next_service.name,
                                'style_group': rule.next_service.style_group.name if rule.next_service.style_group else None,
                                'color': rule.next_service.color
                            }

                            # Create suggestion without availability (will be calculated later if needed)
                            suggestions.append({
                                'suggestion_id': suggestion_id,
                                'suggested_appointment_services': suggested_service,
                                'suggested_appointment_add_ons': [],  # Will be populated below
                                'availability': []  # Will be calculated later if needed
                            })
                            suggestion_id += 1
                        break

            # Helper function to calculate addon suggestions for a specific date
            def get_addon_suggestions_for_date(target_date):
                """Calculate addon suggestions for a specific target date"""
                days_since_target_date = (target_date - last_appointment.start_time.date()).days

                suggested_addons = []

                # Check if customer is new (first appointment was recent)
                customer_appointments_count = Appointment.objects.filter(
                    customer=customer,
                    employee__business=business,
                    status='completed'
                ).count()

                # Customer is new if they have no completed appointments
                is_new_client = customer_appointments_count < 1

                # Get addons from last appointment for rule matching
                last_appointment_addon_ids = set()
                for app_addon in last_appointment.appointment_add_ons.all():
                    last_appointment_addon_ids.add(app_addon.add_on.id)

                # Get all active addon suggestion rules for this business
                addon_rules = AddonSuggestionRule.objects.filter(
                    business=business,
                    is_active=True
                )

                # Apply each rule's criteria
                for rule in addon_rules:
                    logger.info(f"Checking addon rule for {target_date}: {rule.suggested_addon.name} (min_days: {rule.min_days_since}, max_days: {rule.max_days_since}, days_since_target: {days_since_target_date})")

                    # Check new client criteria
                    if rule.new_client_only and not is_new_client:
                        logger.info(f"  ❌ Skipping {rule.suggested_addon.name}: not a new client")
                        continue

                    # Check last addon criteria
                    if rule.last_addon is not None:
                        # This rule requires a specific addon from the last appointment
                        if rule.last_addon.id not in last_appointment_addon_ids:
                            logger.info(f"  ❌ Skipping {rule.suggested_addon.name}: last addon {rule.last_addon.name} not in last appointment")
                            continue

                    # Check timing constraints (apply to all rules, not just those with last_addon)
                    if rule.min_days_since is not None and days_since_target_date < rule.min_days_since:
                        logger.info(f"  ❌ Skipping {rule.suggested_addon.name}: days_since_target ({days_since_target_date}) < min_days_since ({rule.min_days_since})")
                        continue
                    if rule.max_days_since is not None and days_since_target_date >= rule.max_days_since:
                        logger.info(f"  ❌ Skipping {rule.suggested_addon.name}: days_since_target ({days_since_target_date}) >= max_days_since ({rule.max_days_since})")
                        continue

                    # If we get here, the rule applies
                    logger.info(f"  ✅ Adding addon suggestion for {target_date}: {rule.suggested_addon.name}")
                    suggested_addons.append({
                        'id': rule.suggested_addon.id,
                        'short_name': rule.suggested_addon.short_name or rule.suggested_addon.name,
                        'color': rule.suggested_addon.color
                    })

                return suggested_addons

            # Add suggested add-ons to all suggestions and calculate availability
            if suggestions:
                for suggestion in suggestions:
                    # Calculate availability based on whether requested_date is provided
                    if requested_date_provided:
                        # Single date availability calculation
                        service_id = suggestion['suggested_appointment_services']['id']

                        # Calculate addon suggestions for the specific requested date
                        suggested_addons = get_addon_suggestions_for_date(requested_date)
                        suggestion['suggested_appointment_add_ons'] = suggested_addons

                        addon_ids = [addon['id'] for addon in suggested_addons]
                        logger.info(f"Calculating availability for service {service_id} with add-ons {addon_ids} on date {requested_date}")

                        # Get the service object for availability calculation
                        from services.models import Service
                        try:
                            service_obj = Service.objects.get(id=service_id)
                            availability = get_employee_availability(service_obj, addon_ids, requested_date)
                            suggestion['availability'] = availability
                        except Service.DoesNotExist:
                            logger.error(f"Service with ID {service_id} not found")
                            suggestion['availability'] = []
                    else:
                        # Calculate availability for eligible time windows when no specific date provided
                        service_id = suggestion['suggested_appointment_services']['id']

                        # Find the service suggestion rule to get the eligible time window
                        from services.models import Service
                        try:
                            service_obj = Service.objects.get(id=service_id)

                            # Find the rule that matches this service suggestion
                            matching_rule = None
                            for style_group in last_style_groups:
                                rule = LashServiceSuggestionRule.objects.filter(
                                    business=business,
                                    last_service_style_group=style_group,
                                    next_service=service_obj,
                                    is_active=True
                                ).first()
                                if rule:
                                    matching_rule = rule
                                    break

                            if matching_rule:
                                # Calculate eligible date range
                                from datetime import timedelta
                                last_appointment_date = last_appointment.start_time.date()
                                start_date = last_appointment_date + timedelta(days=max(matching_rule.min_days_since, days_since_today))
                                end_date = last_appointment_date + timedelta(days=matching_rule.max_days_since - 1)

                                # Ensure we don't check past dates
                                today = timezone.now().date()
                                if start_date < today:
                                    start_date = today

                                logger.info(f"Checking availability for service {service_id} from {start_date} to {end_date}")

                                # Check if this is a fullset service (typically has longer time windows)
                                is_fullset = 'fullset' in service_obj.name.lower()

                                availability = []
                                current_date = start_date

                                if is_fullset:
                                    # For fullset: find first available slot and stop
                                    max_days_to_check = 14  # Check up to 2 weeks ahead
                                    days_checked = 0

                                    while current_date <= end_date and days_checked < max_days_to_check:
                                        # Calculate addons for this specific date
                                        date_suggested_addons = get_addon_suggestions_for_date(current_date)
                                        date_addon_ids = [addon['id'] for addon in date_suggested_addons]

                                        date_availability = get_employee_availability(service_obj, date_addon_ids, current_date)
                                        if date_availability:  # Found availability, stop here
                                            availability = date_availability
                                            # Update the suggestion with addons for this date
                                            suggestion['suggested_appointment_add_ons'] = date_suggested_addons
                                            logger.info(f"Found first available slot for fullset on {current_date} with addons {[a['short_name'] for a in date_suggested_addons]}")
                                            break
                                        current_date += timedelta(days=1)
                                        days_checked += 1
                                else:
                                    # For refills: check all dates in the eligible window
                                    # Use addons for the first eligible date (since refill windows are shorter)
                                    first_date_addons = get_addon_suggestions_for_date(current_date)
                                    first_date_addon_ids = [addon['id'] for addon in first_date_addons]
                                    suggestion['suggested_appointment_add_ons'] = first_date_addons

                                    while current_date <= end_date:
                                        date_availability = get_employee_availability(service_obj, first_date_addon_ids, current_date)
                                        if date_availability:
                                            # Merge availability from this date
                                            for emp_avail in date_availability:
                                                # Find existing employee or add new one
                                                existing_emp = next((emp for emp in availability if emp['employee_id'] == emp_avail['employee_id']), None)
                                                if existing_emp:
                                                    existing_emp['slots'].extend(emp_avail['slots'])
                                                else:
                                                    availability.append(emp_avail)
                                        current_date += timedelta(days=1)

                                suggestion['availability'] = availability
                            else:
                                logger.warning(f"No matching rule found for service {service_id}")
                                suggestion['availability'] = []

                        except Service.DoesNotExist:
                            logger.error(f"Service with ID {service_id} not found")
                            suggestion['availability'] = []

            # If no service suggestions, we don't create addon-only suggestions
            # (addons are now calculated dynamically for each service/date combination)

            # Add employee information to last appointment
            last_appointment_data = {
                'id': last_appointment.id,
                'date': last_appointment.start_time.isoformat(),
                'employee_id': last_appointment.employee.id,
                'employee_name': f"{last_appointment.employee.user.first_name} {last_appointment.employee.user.last_name}".strip() or f"Employee #{last_appointment.employee.id}",
                'appointment_services': last_services,
                'appointment_add_ons': last_add_ons,
                'days_since_today': days_since_today
            }

            # Add days_since_requested_date if a specific date was requested
            if requested_date_provided:
                days_since_requested_date = (requested_date - last_appointment.start_time.date()).days
                last_appointment_data['days_since_requested_date'] = days_since_requested_date

            response_data = {
                'customer_id': int(customer_id),
                'last_appointment': last_appointment_data,
                'suggestions': suggestions
            }
            if requested_date_provided:
                response_data['requested_date'] = requested_date_str

            return Response(response_data)

        except Exception as e:
            logger.exception(f'Error generating service suggestions: {str(e)}')
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BusinessCustomerFormCompletionView(APIView):
    """
    API endpoint for business staff to check form completion status for business customers.
    Uses the logged-in user's business ID.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """
        Check form completion status for business customers.
        
        Query Parameters:
        - customer_id (optional): Specific customer ID to check
        - all_customers (optional): If true, return status for all customers
        
        Returns:
        - List of required forms and their completion status
        - Overall completion status
        - Missing forms that need to be signed
        """
        try:
            # Get the employee profile for the current user
            from employees.models import Employee
            from django.shortcuts import get_object_or_404

            employee = get_object_or_404(Employee, user=request.user)
            business = employee.business

            # Check if we want all customers or a specific customer
            customer_id = request.query_params.get('customer_id')
            all_customers = request.query_params.get('all_customers', 'false').lower() == 'true'

            if customer_id:
                # Get specific customer
                business_customer = get_object_or_404(BusinessCustomer, id=customer_id, business=business)
                customers_to_check = [business_customer]
            elif all_customers:
                # Get all customers for this business
                customers_to_check = BusinessCustomer.objects.filter(business=business)
            else:
                return Response({
                    'error': 'Either customer_id or all_customers=true parameter is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get all required forms for this business
            required_forms = BusinessRequiredForm.objects.filter(
                business=business,
                is_required=True
            ).select_related('form_template').order_by('order', 'form_template__name')

            if customer_id:
                # Single customer response
                return self._get_single_customer_response(business_customer, required_forms)
            else:
                # Multiple customers response
                return self._get_all_customers_response(customers_to_check, required_forms)

        except BusinessCustomer.DoesNotExist:
            return Response({
                'error': 'Customer not found or does not belong to this business'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error checking form completion: {e}")
            return Response({
                'error': 'Failed to check form completion status'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_single_customer_response(self, business_customer, required_forms):
        """Generate response for a single customer"""
        completion_status = []
        missing_forms = []

        for required_form in required_forms:
            form_template = required_form.form_template
            
            # Check if customer has submitted this form
            submission = FormSubmission.objects.filter(
                business_customer=business_customer,
                form_template=form_template,
                status__in=['Submitted', 'Approved']
            ).first()
            
            # Check if customer has signed this form (if signature is required)
            has_signature = False
            signed_at = None
            if submission:
                signature = submission.signatures.filter(signer_type='customer').first()
                if signature:
                    has_signature = True
                    signed_at = signature.created_at
            
            # Check if marked as signed in BusinessCustomer tracking
            is_tracked_as_signed = business_customer.has_signed_form(form_template.id)
            
            # Simplified: Only track if form is submitted (which should include signature if required)
            is_submitted = submission is not None
            
            form_status = {
                'form_template_id': form_template.id,
                'form_name': form_template.name,
                'document_type': form_template.document_type,
                'is_submitted': is_submitted,
                'submitted_at': submission.created_at if submission else None,
                'required_for_new_customers': required_form.required_for_new_customers,
                'required_for_existing_customers': required_form.required_for_existing_customers,
                'order': required_form.order
            }
            
            completion_status.append(form_status)
            
            # Add to missing forms if not submitted
            if not is_submitted:
                missing_forms.append({
                    'form_template_id': form_template.id,
                    'form_name': form_template.name,
                    'document_type': form_template.document_type,
                    'order': required_form.order
                })

        # Get overall completion status
        all_completed = business_customer.has_all_required_forms_signed()
        
        response_data = {
            'business_customer_id': business_customer.id,
            'customer_name': f"{business_customer.customer.user.first_name} {business_customer.customer.user.last_name}".strip() or business_customer.customer.user.email,
            'business_name': business_customer.business.name,
            'all_forms_completed': all_completed,
            'required_forms_count': len(completion_status),
            'completed_forms_count': sum(1 for status in completion_status if status['is_submitted']),
            'missing_forms_count': len(missing_forms),
            'forms_completed_at': business_customer.forms_completed_at,
            'form_status': completion_status,
            'missing_forms': missing_forms
        }

        return Response(response_data)

    def _get_all_customers_response(self, customers, required_forms):
        """Generate response for all customers"""
        customers_data = []
        
        for business_customer in customers:
            customer_completion = []
            missing_forms = []
            
            for required_form in required_forms:
                form_template = required_form.form_template
                
                # Check if customer has submitted this form
                submission = FormSubmission.objects.filter(
                    business_customer=business_customer,
                    form_template=form_template,
                    status__in=['Submitted', 'Approved']
                ).first()
                
                # Check if customer has signed this form (if signature is required)
                has_signature = False
                if submission:
                    signature = submission.signatures.filter(signer_type='customer').first()
                    if signature:
                        has_signature = True
                
                # Simplified: Only track if form is submitted (which should include signature if required)
                is_submitted = submission is not None
                
                # Add to missing forms if not submitted
                if not is_submitted:
                    missing_forms.append({
                        'form_template_id': form_template.id,
                        'form_name': form_template.name,
                        'document_type': form_template.document_type,
                        'order': required_form.order
                    })
                
                customer_completion.append({
                    'form_template_id': form_template.id,
                    'form_name': form_template.name,
                    'is_submitted': is_submitted,
                    'submitted_at': submission.created_at if submission else None
                })
            
            all_completed = business_customer.has_all_required_forms_signed()
            
            customers_data.append({
                'business_customer_id': business_customer.id,
                'customer_name': f"{business_customer.customer.user.first_name} {business_customer.customer.user.last_name}".strip() or business_customer.customer.user.email,
                'all_forms_completed': all_completed,
                'completed_forms_count': sum(1 for status in customer_completion if status['is_submitted']),
                'missing_forms_count': len(missing_forms),
                'forms_completed_at': business_customer.forms_completed_at,
                'missing_forms': missing_forms
            })
        
        # Calculate business-wide statistics
        total_customers = len(customers_data)
        customers_with_all_forms = sum(1 for customer in customers_data if customer['all_forms_completed'])
        
        response_data = {
            'business_name': customers[0].business.name if customers else '',
            'total_customers': total_customers,
            'customers_with_all_forms': customers_with_all_forms,
            'completion_rate': (customers_with_all_forms / total_customers * 100) if total_customers > 0 else 0,
            'required_forms': [
                {
                    'form_template_id': form.form_template.id,
                    'form_name': form.form_template.name,
                    'document_type': form.form_template.document_type,
                    'order': form.order
                }
                for form in required_forms
            ],
            'customers': customers_data
        }
        
        return Response(response_data)


class CustomerFormCompletionView(APIView):
    """
    API endpoint for customers to check their own form completion status.
    Uses the current logged-in customer's information and business context.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """
        Check form completion status for the current logged-in customer.

        Query Parameters:
        - business_id (optional): Specific business ID to check forms for

        Returns:
        - List of required forms and their completion status
        - Overall completion status
        - Missing forms that need to be signed
        """
        try:
            from customers.models import CustomerProfile
            from django.shortcuts import get_object_or_404

            # Get the current user's customer profile
            customer_profile = get_object_or_404(CustomerProfile, user=request.user)

            # Get business ID from query parameters
            business_id = request.query_params.get('business_id')

            if business_id:
                # Get the business customer relationship for the specific business
                try:
                    business = get_object_or_404(Business, id=business_id)
                    business_customer = BusinessCustomer.objects.get(
                        customer=customer_profile,
                        business=business
                    )
                except BusinessCustomer.DoesNotExist:
                    # If no relationship exists, create one for new customers
                    business_customer = BusinessCustomer.objects.create(
                        customer=customer_profile,
                        business=business
                    )
                    print(f"🔗 Created new BusinessCustomer relationship for {request.user.email} with {business.name}")
            else:
                # Fallback to first business relationship if no business_id provided
                business_customer = BusinessCustomer.objects.filter(customer=customer_profile).first()

                if not business_customer:
                    return Response({
                        'error': 'No business relationships found for this customer. Please provide a business_id parameter.'
                    }, status=status.HTTP_404_NOT_FOUND)

            # Get all required forms for this business
            required_forms = BusinessRequiredForm.objects.filter(
                business=business_customer.business,
                is_required=True
            ).select_related('form_template').order_by('order', 'form_template__name')

            completion_status = []
            missing_forms = []

            for required_form in required_forms:
                form_template = required_form.form_template
                
                # Check if customer has submitted this form
                submission = FormSubmission.objects.filter(
                    business_customer=business_customer,
                    form_template=form_template,
                    status__in=['Submitted', 'Approved']
                ).first()
                
                # Check if customer has signed this form (if signature is required)
                has_signature = False
                signed_at = None
                if submission:
                    signature = submission.signatures.filter(signer_type='customer').first()
                    if signature:
                        has_signature = True
                        signed_at = signature.created_at
                
                # Check if marked as signed in BusinessCustomer tracking
                is_tracked_as_signed = business_customer.has_signed_form(form_template.id)
                
                # Simplified: Only track if form is submitted (which should include signature if required)
                is_submitted = submission is not None
                
                form_status = {
                    'form_template_id': form_template.id,
                    'form_name': form_template.name,
                    'document_type': form_template.document_type,
                    'is_submitted': is_submitted,
                    'submitted_at': submission.created_at if submission else None,
                    'required_for_new_customers': required_form.required_for_new_customers,
                    'required_for_existing_customers': required_form.required_for_existing_customers,
                    'order': required_form.order
                }
                
                completion_status.append(form_status)
                
                # Add to missing forms if not submitted
                if not is_submitted:
                    missing_forms.append({
                        'form_template_id': form_template.id,
                        'form_name': form_template.name,
                        'document_type': form_template.document_type,
                        'order': required_form.order
                    })

            # Get overall completion status
            all_completed = business_customer.has_all_required_forms_signed()
            
            response_data = {
                'business_customer_id': business_customer.id,
                'customer_name': f"{business_customer.customer.user.first_name} {business_customer.customer.user.last_name}".strip() or business_customer.customer.user.email,
                'business_name': business_customer.business.name,
                'all_forms_completed': all_completed,
                'required_forms_count': len(completion_status),
                'completed_forms_count': sum(1 for status in completion_status if status['is_submitted']),
                'missing_forms_count': len(missing_forms),
                'forms_completed_at': business_customer.forms_completed_at,
                'form_status': completion_status,
                'missing_forms': missing_forms
            }

            return Response(response_data)

        except BusinessCustomer.DoesNotExist:
            return Response({
                'error': 'Customer not found for this business'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error checking customer form completion: {e}")
            return Response({
                'error': 'Failed to check form completion status'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)