/**
 * Navigation utilities for booking flow
 * Centralizes consent form status checking and routing logic
 */

import { authService } from '../features/auth/services/authApi';
import { checkUserConsentStatus } from '../api/consentApi';
import { useAuthStore } from '../stores/authStore';
import { logBookingFlow, logConsentStatus, logApiError } from './debugUtils';

/**
 * Determines the next step in the booking flow after time slot selection
 * @param {Object} user - Current authenticated user
 * @param {Function} navigate - React Router navigate function
 * @returns {Promise<string>} - The route that was navigated to
 */
export const handlePostTimeSlotSelection = async (user, navigate) => {
  // Step 1: Check authentication first - no API calls yet
  if (!user) {
    logBookingFlow('Time Slot Selection', { user: null }, 'Redirect to login - not authenticated');
    navigate('/login');
    return '/login';
  }

  // Step 2: Verify token is still valid (basic check)
  const token = localStorage.getItem('auth_token');
  if (!token) {
    logBookingFlow('Time Slot Selection', { userId: user.id }, 'Redirect to login - no token');
    navigate('/login');
    return '/login';
  }

  // Step 3: Check auth store for consent status (no API calls)
  const authStore = useAuthStore.getState();
  const currentStatus = authStore.getConsentStatus();

  logConsentStatus(user.id, currentStatus, 'auth-store-navigation');

  // Step 4: Make navigation decision based on available data
  if (!currentStatus) {
    // No consent status in auth store - this means we need to check during login
    // Don't make API calls here, redirect to login to refresh everything
    logBookingFlow('Time Slot Selection', { userId: user.id }, 'No consent status - redirect to login for refresh');
    navigate('/login');
    return '/login';
  }

  // Step 5: Navigate based on consent status
  const hasCompletedForms = authStore.hasCompletedAllForms();

  if (hasCompletedForms) {
    logBookingFlow('Time Slot Selection', { userId: user.id, formsCompleted: true }, 'Navigate to review');
    navigate('/review-booking');
    return '/review-booking';
  } else {
    logBookingFlow('Time Slot Selection', { userId: user.id, formsNeeded: true }, 'Navigate to consent form');
    navigate('/consent-form');
    return '/consent-form';
  }
};

/**
 * Handles post-login navigation for booking flow
 * @param {Object} user - Current authenticated user
 * @param {Function} navigate - React Router navigate function
 * @param {boolean} isBookingFlow - Whether this is part of a booking flow
 * @returns {Promise<string>} - The route that was navigated to
 */
export const handlePostLoginNavigation = async (user, navigate, isBookingFlow = false) => {
  if (!isBookingFlow) {
    // Regular login, go to home page
    navigate('/');
    return '/';
  }

  // Booking flow - check consent status and route accordingly
  return await handlePostTimeSlotSelection(user, navigate);
};

/**
 * Validates if user can proceed to a specific step in booking flow
 * @param {string} step - The step to validate ('consent', 'review', 'payment')
 * @param {Object} bookingData - Current booking data
 * @param {Object} user - Current authenticated user
 * @returns {boolean} - Whether user can proceed to the step
 */
export const canProceedToBookingStep = (step, bookingData, user) => {
  if (!user) {
    return false;
  }

  // Base requirements for any booking step
  const hasBasicBookingData = !!(
    bookingData.selectedService &&
    bookingData.selectedEmployee &&
    bookingData.selectedDate &&
    bookingData.selectedTime
  );

  switch (step) {
    case 'consent':
      return hasBasicBookingData;

    case 'review':
      // Use single source of truth for consent status from auth store
      const authStore = useAuthStore.getState();
      return hasBasicBookingData && authStore.hasCompletedAllForms();

    case 'payment':
      const authStoreForPayment = useAuthStore.getState();
      return hasBasicBookingData &&
             authStoreForPayment.hasCompletedAllForms() &&
             !!(bookingData.customerInfo.name &&
                bookingData.customerInfo.email &&
                bookingData.customerInfo.phone);

    default:
      return false;
  }
};

/**
 * Gets the appropriate redirect route based on current booking state
 * @param {Object} bookingData - Current booking data
 * @param {Object} user - Current authenticated user
 * @returns {string} - The route to redirect to
 */
export const getBookingRedirectRoute = (bookingData, user) => {
  if (!user) {
    return '/login';
  }

  if (!canProceedToBookingStep('consent', bookingData, user)) {
    return '/';
  }

  // Use single source of truth for consent status from auth store
  const authStore = useAuthStore.getState();
  if (!authStore.hasCompletedAllForms()) {
    return '/consent-form';
  }

  return '/review-booking';
};

export default {
  handlePostTimeSlotSelection,
  handlePostLoginNavigation,
  canProceedToBookingStep,
  getBookingRedirectRoute
};
