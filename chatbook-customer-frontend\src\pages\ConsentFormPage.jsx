import { useState, useEffect } from 'react';
import publicApi from '../utils/publicApi';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { useBookingStore } from '../stores/bookingStore';
import { canProceedToBookingStep, getBookingRedirectRoute } from '../utils/navigationUtils';
import './ConsentFormPage.css';
import SignaturePad from '../components/SignaturePad';
import { submitConsentForm, checkUserConsentStatus, getFormTemplate } from "../api/consentApi";
import { saveFormToPdf } from "../utils/pdfUtils";
import { authService } from '../features/auth/services/authApi';

const ConsentFormPage = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { user, isAuthenticated } = useAuthStore();
    const {
        bookingData,
        setConsentData,
        canProceedToStep,
        updateBookingData,
        consentStatus
    } = useBookingStore();
    // Keep local signature state in sync with BookingContext so it persists
    const [signature, setSignature] = useState(bookingData?.consentData?.signature || '');

    // Pull in latest saved signature when user revisits this page
    useEffect(() => {
        if (bookingData?.consentData?.signature !== signature) {
            setSignature(bookingData?.consentData?.signature || '');
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [bookingData?.consentData?.signature]);

    // Auto-redirect to review if user has already signed (but allow coming back from review)
    useEffect(() => {
        // Only auto-redirect if user came from a different page (not from review)
        const fromReview = location.state?.fromReview;

        if (isAuthenticated && bookingData?.consentData?.consentAgreed && bookingData?.consentData?.signature && !fromReview) {
            console.log('✅ User has already signed consent - redirecting to review');
            navigate('/review-booking');
        }
    }, [isAuthenticated, bookingData?.consentData?.consentAgreed, bookingData?.consentData?.signature, navigate, location.state]);
    const [agreeToElectronicRecords, setAgreeToElectronicRecords] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);
    const isAlreadySigned = bookingData?.consentData?.consentAgreed && bookingData?.consentData?.signature;
    const [isOrderSummaryExpanded, setIsOrderSummaryExpanded] = useState(false);

    // Multi-form state
    const [missingForms, setMissingForms] = useState([]);
    const [currentFormIndex, setCurrentFormIndex] = useState(0);
    const [formsStatus, setFormsStatus] = useState(null);
    const [loadingForms, setLoadingForms] = useState(true);
    const [error, setError] = useState(null);
    const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false); // Prevent multiple calls

    /* ------------------------------------------------------------------
       Dynamic form template + answers state
    ------------------------------------------------------------------ */
    // useApi attaches Authorization header – for public template listing we need *no* auth header.
    // So we directly use publicApi here.
    const fetchTemplates = async (params = {}) => {
        const res = await publicApi.get('/forms/templates/', { params });
        return res.data;
    };
    const [template, setTemplate] = useState(null);
    const [answers, setAnswers] = useState({});

    const setAnswer = (qid, value) => {
        setAnswers(prev => ({ ...prev, [qid]: value }));
    };

    // Load missing forms and current form template
    useEffect(() => {
        const loadMissingForms = async () => {
            // Prevent multiple calls
            if (hasAttemptedLoad) {
                console.log('🚫 Already attempted to load forms, skipping');
                return;
            }

            try {
                console.log('🔄 Starting to load missing forms (first attempt)');
                setHasAttemptedLoad(true);
                setLoadingForms(true);
                setError(null);

                // Get business ID from booking data
                const businessId = bookingData?.selectedService?.business || bookingData?.businessId || 1;
                console.log('🏢 Using business ID for consent check:', businessId);

                // Get user's forms status with error handling
                const status = await checkUserConsentStatus(businessId);
                console.log('✅ Successfully loaded consent status:', status);
                setFormsStatus(status);

                // Note: We no longer redirect here if all forms are completed
                // That routing decision is made earlier in the login/booking flow
                // This page should only be reached if user needs to complete forms

                // Set missing forms
                setMissingForms(status.missing_forms || []);

                // Load the first missing form template
                if (status.missing_forms && status.missing_forms.length > 0) {
                    const firstForm = status.missing_forms[0];
                    const templateData = await getFormTemplate(firstForm.form_template_id);
                    setTemplate(templateData);
                    console.log(`📋 Loaded form template: ${firstForm.form_name}`);
                } else if (status.all_forms_completed) {
                    // If we somehow end up here with completed forms, just log it
                    console.log('ℹ️ All forms completed but user is on consent page - this should not happen in normal flow');
                }
            } catch (err) {
                console.error('❌ Failed to load missing forms:', err);

                // Check if this is an authentication error
                if (err.message && err.message.includes('Authentication expired')) {
                    // Error already handled by checkUserConsentStatus, just return
                    console.log('🔒 Authentication error handled, not retrying');
                    return;
                }

                // For other errors, set error state and don't retry
                setError('Failed to load consent forms. Please try refreshing the page.');
                console.log('💥 Setting error state, will not retry automatically');
            } finally {
                setLoadingForms(false);
            }
        };

        // Only attempt if authenticated, has user, and hasn't attempted yet
        if (isAuthenticated && user && !hasAttemptedLoad) {
            console.log('🎯 Conditions met for loading forms - user properly authenticated');
            loadMissingForms();
        } else if (!isAuthenticated || !user) {
            console.log('🔒 User not properly authenticated on consent page, redirecting to login');
            navigate('/login');
        } else if (hasAttemptedLoad) {
            console.log('⏭️ Already attempted to load forms, skipping useEffect');
        }
    }, [isAuthenticated, user, navigate, hasAttemptedLoad]);

    // Consolidated validation useEffect (no API calls)
    useEffect(() => {
        console.log('🔍 ConsentFormPage validation - isAuthenticated:', isAuthenticated);
        console.log('🔍 ConsentFormPage validation - hasAttemptedLoad:', hasAttemptedLoad);

        // Check if logout was recently triggered to prevent loops
        const logoutTriggered = sessionStorage.getItem('auth_logout_triggered');
        if (logoutTriggered) {
            console.log('🚫 Logout was recently triggered, not redirecting to prevent loop');
            setError('Authentication session expired. Please refresh the page and log in again.');
            return;
        }

        // Check authentication first
        if (!isAuthenticated) {
            console.log('🔒 User not authenticated, redirecting to login');
            navigate('/login?reason=auth_required');
            return;
        }

        // Check if we have the required booking data to proceed to consent
        if (!canProceedToBookingStep('consent', bookingData, user)) {
            console.log('📋 Missing required booking data, redirecting to appropriate page');
            const redirectRoute = getBookingRedirectRoute(bookingData, user);
            navigate(redirectRoute);
            return;
        }

        console.log('✅ ConsentFormPage validation passed - booking data available');
    }, [isAuthenticated, navigate, bookingData, user]);

    const handleBack = () => {
        // Go back to the booking calendar page
        navigate('/');
    };

    const handleNext = async () => {
        // If already signed, just navigate to review
        if (isAlreadySigned) {
            console.log('✅ User already signed, navigating to review');
            navigate('/review-booking');
            return;
        }

        // Validate signature and electronic records agreement
        if (!agreeToElectronicRecords || !signature.trim()) {
            alert('Please complete the signature and agree to electronic records.');
            return;
        }

        // Validate business customer ID
        if (!formsStatus?.business_customer_id) {
            console.error('❌ Missing business_customer_id:', formsStatus);
            alert('Unable to submit form: missing business customer information. Please try refreshing the page.');
            return;
        }

        setIsLoading(true);
        const currentForm = missingForms[currentFormIndex];
        console.log(`📝 Processing form submission for: ${currentForm?.form_name}`);
        console.log(`📋 Business customer ID: ${formsStatus?.business_customer_id}`);

        try {
            // Submit the current form
            const payload = {
                form_template: template?.id,
                business: bookingData?.selectedService?.business || bookingData?.businessId || 1,
                business_customer: formsStatus?.business_customer_id,
                content: {
                    answers,
                    signature_data: signature,
                    electronic_records_agreed: agreeToElectronicRecords
                },
            };

            await submitConsentForm(payload);
            console.log(`✅ Form "${currentForm?.form_name}" submitted successfully`);

            // Check if there are more forms to complete
            const nextFormIndex = currentFormIndex + 1;
            if (nextFormIndex < missingForms.length) {
                // Load next form
                console.log(`📋 Loading next form (${nextFormIndex + 1}/${missingForms.length})`);
                const nextForm = missingForms[nextFormIndex];

                try {
                    const nextTemplate = await getFormTemplate(nextForm.form_template_id);
                    setTemplate(nextTemplate);
                    setCurrentFormIndex(nextFormIndex);

                    // Reset form state for next form
                    setSignature('');
                    setAnswers({});
                    setAgreeToElectronicRecords(false);

                    console.log(`📋 Loaded next form: ${nextForm.form_name}`);
                } catch (error) {
                    console.error('Error loading next form:', error);
                    alert('Error loading next form. Please try again.');
                }
            } else {
                // All forms completed - update auth store and navigate to review
                console.log('🎉 All forms completed!');

                // Refresh consent status from API to update auth store (single source of truth)
                try {
                    const businessId = bookingData?.selectedService?.business || bookingData?.businessId || 1;
                    const freshStatus = await authService.refreshConsentStatus(businessId);
                    // Update auth store with fresh data
                    updateConsentStatus(freshStatus);
                    console.log('✅ Refreshed consent status in auth store:', freshStatus);
                } catch (error) {
                    console.warn('⚠️ Failed to refresh consent status, but forms were submitted');
                    // Manually update auth store as fallback
                    updateConsentStatus({ all_forms_completed: true, missing_forms: [] });
                }

                console.log('🎉 All consent forms completed successfully - navigating to review');

                // Show success message briefly before navigation
                setShowSuccessMessage(true);

                // Navigate to review page after a short delay to show success
                setTimeout(() => {
                    navigate('/review-booking');
                }, 1000);
            }

        } catch (error) {
            console.error('❌ Consent submission failed:', error);
            alert('Could not save the consent form. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const clearSignature = () => {
        // Allow clearing signature if coming back from review, otherwise only if not already signed
        const fromReview = location.state?.fromReview;
        if (isAlreadySigned && !fromReview) return; // do nothing once signed unless coming from review

        console.log('🧹 Clearing signature (from review:', fromReview, ')');
        setSignature('');

        // Remove signature + consent in booking context so user must sign again
        updateBookingData({
            consentData: {
                ...bookingData.consentData,
                signature: '',
                consentAgreed: false,
                consentTimestamp: null,
            },
            step: 'consent',
        });
    };

    const getCurrentDateTime = () => {
        const now = new Date();
        return now.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        }) + ' - ' + now.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    // If we don't have required booking data, the useEffect will redirect
    if (!canProceedToBookingStep('consent', bookingData, user)) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading booking details...</p>
                </div>
            </div>
        );
    }

    const totalCost = parseFloat(bookingData.selectedService?.price || 0) +
                     (bookingData.selectedAddOns?.reduce((sum, addon) => sum + parseFloat(addon.price || 0), 0) || 0);

    // Debug employee data
    console.log('ConsentFormPage - Employee data:', bookingData.selectedEmployee);

    /* ------------------------------------------------------------------
       Renderer for each question type coming from template.content.questions
    ------------------------------------------------------------------ */
    const renderQuestion = (q) => {
        switch (q.type) {
            case 'text': {
                // Support the textStyle prop coming from builder: Heading, Subheading, Paragraph
                const style = q.textStyle || 'Paragraph';
                if (style === 'Heading') {
                    return (
                        <h2 key={q.id} className="text-xl lg:text-2xl font-bold text-gray-900 mb-4 whitespace-pre-wrap">
                            {q.textContent || q.label}
                        </h2>
                    );
                }
                if (style === 'Subheading') {
                    return (
                        <h3 key={q.id} className="text-lg font-semibold text-gray-900 mb-2 whitespace-pre-wrap">
                            {q.textContent || q.label}
                        </h3>
                    );
                }
                // Default paragraph
                return (
                    <p key={q.id} className="mb-4 text-gray-700 whitespace-pre-wrap">
                        {q.textContent || q.label}
                    </p>
                );
            }
            case 'heading':
            case 'subheading':
                return (
                    <h3 key={q.id} className="font-bold text-gray-900 mt-6 mb-2">
                        {q.textContent || q.label}
                    </h3>
                );
            case 'short':
                return (
                    <div key={q.id} className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">{q.label}</label>
                        <input
                            className="w-full border rounded px-3 py-2"
                            value={answers[q.id] || ''}
                            onChange={(e) => setAnswer(q.id, e.target.value)}
                        />
                    </div>
                );
            case 'long':
                return (
                    <div key={q.id} className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">{q.label}</label>
                        <textarea
                            className="w-full border rounded px-3 py-2"
                            rows={4}
                            value={answers[q.id] || ''}
                            onChange={(e) => setAnswer(q.id, e.target.value)}
                        />
                    </div>
                );
            case 'multiple':
                return (
                    <div key={q.id} className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">{q.label}</label>
                        {q.options?.map((opt, idx) => (
                            <div key={idx} className="flex items-center mb-1">
                                <input
                                    type="radio"
                                    id={`${q.id}-${idx}`}
                                    name={`q-${q.id}`}
                                    value={opt}
                                    checked={answers[q.id] === opt}
                                    onChange={() => setAnswer(q.id, opt)}
                                    className="mr-2"
                                />
                                <label htmlFor={`${q.id}-${idx}`} className="text-sm text-gray-700">{opt}</label>
                            </div>
                        ))}
                    </div>
                );
            case 'signature':
                if (isAlreadySigned) {
                    return (
                        <div key={q.id} className="signature-section mt-8">
                            <p className="text-green-700 font-semibold mb-2">Signature recorded ✔</p>
                            <img src={signature} alt="Signed" className="border border-gray-400 w-full" />
                        </div>
                    );
                }
                return (
                    <div key={q.id} className="signature-section mt-8">
                        <div className="signature-checkbox mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <span className="text-red-500">Please sign here *</span>
                            </label>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id={`electronic-records-${q.id}`}
                                    checked={agreeToElectronicRecords}
                                    onChange={(e) => setAgreeToElectronicRecords(e.target.checked)}
                                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                />
                                <label htmlFor={`electronic-records-${q.id}`} className="text-sm text-gray-700">
                                    I agree to use <span className="text-blue-600 underline">electronic records and signatures.</span>
                                </label>
                            </div>
                        </div>

                        <SignaturePad
                            value={signature}
                            onChange={(dataUrl) => {
                                setSignature(dataUrl);
                                setAnswer(q.id, dataUrl);
                            }}
                        />
                        <div className="flex justify-between items-center mt-2">
                            <div>
                                {(signature || location.state?.fromReview) && (
                                    <button
                                        type="button"
                                        onClick={clearSignature}
                                        className="text-sm text-red-600 hover:text-red-800 underline"
                                        disabled={isAlreadySigned && !location.state?.fromReview}
                                    >
                                        Clear Signature
                                    </button>
                                )}
                            </div>
                            <div className="text-sm text-gray-600">
                                <span>{getCurrentDateTime()}</span>
                            </div>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <div className="min-h-screen bg-white">
            {/* Simple Top Header */}
            <div className="bg-white">
                <div className="max-w-6xl mx-auto border-b border-gray-200 px-6 py-3">
                    <div className="flex justify-end items-center">
                        <div className="flex items-center space-x-3">
                            <span className="text-sm text-gray-700">Welcome, DylanStylistTest Z</span>
                            <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                DZ
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Mobile Order Summary - Collapsible */}
            <div className="lg:hidden bg-white border-b border-gray-200">
                <div className="max-w-6xl mx-auto px-6">
                    <button
                        onClick={() => setIsOrderSummaryExpanded(!isOrderSummaryExpanded)}
                        className="w-full py-4 flex justify-between items-center text-left"
                    >
                        <h3 className="text-lg font-semibold text-gray-900">Order Summary</h3>
                        <span className="text-gray-500 text-xl">
                            {isOrderSummaryExpanded ? '−' : '+'}
                        </span>
                    </button>

                    {isOrderSummaryExpanded && (
                        <div className="pb-6 border-t border-gray-200">
                            <div className="pt-4">
                                {/* Date and Time */}
                                <div className="mb-4">
                                    <div className="text-sm text-gray-900 font-medium">
                                        {bookingData.selectedDate ?
                                            new Date(bookingData.selectedDate).toLocaleDateString('en-US', {
                                                weekday: 'short',
                                                month: 'short',
                                                day: 'numeric',
                                                year: 'numeric'
                                            }) : 'Date not selected'
                                        } {bookingData.selectedTime ?
                                            new Date(`2000-01-01 ${bookingData.selectedTime}`).toLocaleTimeString('en-US', {
                                                hour: 'numeric',
                                                minute: '2-digit',
                                                hour12: true
                                            }) : 'Time not selected'
                                        }
                                    </div>
                                </div>

                                {/* Stylist Info */}
                                <div className="mb-4 pb-4 border-b border-gray-300">
                                    <div className="flex items-start space-x-3">
                                        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                                            <span className="text-sm font-medium text-gray-700">
                                                {(bookingData.selectedEmployee?.display_name || bookingData.selectedEmployee?.name || bookingData.selectedEmployee?.full_name)?.charAt(0) || 'S'}
                                            </span>
                                        </div>
                                        <div className="flex-1">
                                            <div className="text-sm text-gray-600">
                                                {bookingData.selectedEmployee?.title || bookingData.selectedEmployee?.role || 'Stylist'}
                                            </div>
                                            <div className="font-medium text-gray-900">
                                                {bookingData.selectedEmployee?.display_name ||
                                                 bookingData.selectedEmployee?.name ||
                                                 bookingData.selectedEmployee?.full_name ||
                                                 'Stylist'}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Services and Total */}
                                <div className="mb-4">
                                    {/* Main Service */}
                                    <div className="flex justify-between items-center mb-2">
                                        <div className="text-sm text-gray-900">
                                            {bookingData.selectedService?.name || 'Classic Lash Fullset(C01-C02)'}
                                        </div>
                                        <div className="font-medium text-gray-900">
                                            ${bookingData.selectedService?.price || '199.00'}
                                        </div>
                                    </div>

                                    {/* Add-ons */}
                                    {bookingData.selectedAddOns && bookingData.selectedAddOns.length > 0 && (
                                        bookingData.selectedAddOns.map((addon, index) => (
                                            <div key={index} className="flex justify-between items-center mb-2">
                                                <div className="text-sm text-gray-900">
                                                    {addon.name}
                                                </div>
                                                <div className="font-medium text-gray-900">
                                                    ${addon.price}
                                                </div>
                                            </div>
                                        ))
                                    )}

                                    {/* Total */}
                                    <div className="flex justify-between items-center text-lg font-semibold mt-4">
                                        <span>Total</span>
                                        <span>${totalCost.toFixed(2)}</span>
                                    </div>
                                </div>

                                {/* Payment Summary */}
                                <div className="pt-4 border-t border-gray-300">
                                    <div className="flex justify-between items-center mb-2">
                                        <span className="text-sm text-gray-900">Total Due Now</span>
                                        <span className="font-medium text-gray-900">$0.00</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-gray-900">Total Due at Business</span>
                                        <span className="font-medium text-gray-900">${totalCost.toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Main Content - Centered with Padding */}
            <div className="max-w-6xl mx-auto">
                <div className="flex">
                    {/* Main Content */}
                    <div className="flex-1 bg-white p-4 lg:p-8">
                            <div className="business-header">
                                <h1 className="text-2xl font-bold text-gray-900 mb-6">Clément Lash</h1>

                                {/* Multi-Form Progress */}
                                {!loadingForms && missingForms.length > 0 && (
                                    <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                                        <div className="flex items-center justify-between mb-2">
                                            <h2 className="text-lg font-semibold text-blue-900">
                                                {missingForms[currentFormIndex]?.form_name || 'Consent Form'}
                                            </h2>
                                            <span className="text-sm text-blue-700 font-medium">
                                                Form {currentFormIndex + 1} of {missingForms.length}
                                            </span>
                                        </div>
                                        {missingForms.length > 1 && (
                                            <div className="w-full bg-blue-200 rounded-full h-2">
                                                <div
                                                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                                    style={{ width: `${((currentFormIndex + 1) / missingForms.length) * 100}%` }}
                                                ></div>
                                            </div>
                                        )}
                                    </div>
                                )}

                                {/* Loading Forms */}
                                {loadingForms && (
                                    <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
                                        <div className="flex items-center">
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                                            <span className="text-gray-700">Loading required forms...</span>
                                        </div>
                                    </div>
                                )}

                                {/* Error State */}
                                {error && (
                                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                                        <div className="flex items-center">
                                            <div className="text-red-600 mr-2">⚠️</div>
                                            <div>
                                                <h3 className="text-red-800 font-semibold">Error Loading Forms</h3>
                                                <p className="text-red-700">{error}</p>
                                                <button
                                                    onClick={() => window.location.reload()}
                                                    className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                                                >
                                                    Refresh Page
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Consent Status Display */}
                                <div className="mb-6 p-4 border rounded-md">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <span className="text-sm font-medium text-gray-700 mr-2">Consent Status:</span>
                                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                                consentStatus === 'signed'
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-gray-100 text-gray-800'
                                            }`}>
                                                {consentStatus === 'signed' ? '✅ SIGNED' : '📝 NOT SIGNED'}
                                            </span>
                                        </div>
                                        {consentStatus === 'signed' && (
                                            <span className="text-xs text-gray-500">
                                                Status preserved across sessions
                                            </span>
                                        )}
                                    </div>
                                </div>

                                {/* Back from Review Status */}
                                {location.state?.fromReview && (
                                    <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md">
                                        <div className="flex items-center">
                                            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                            </svg>
                                            <span className="font-medium">📝 Returned from Review</span>
                                        </div>
                                        <p className="mt-1 text-sm">You can review or modify your consent form below. Your existing signature is preserved.</p>
                                    </div>
                                )}

                                {/* Already Signed Status */}
                                {isAlreadySigned && !location.state?.fromReview && (
                                    <div className="mb-6 p-4 bg-blue-50 border border-blue-200 text-blue-800 rounded-md">
                                        <div className="flex items-center">
                                            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            <span className="font-medium">✅ Consent Form Already Signed</span>
                                        </div>
                                        <p className="mt-1 text-sm">You have already signed this consent form. Click "Next" to proceed to review your booking.</p>
                                    </div>
                                )}
                            </div>

                            <div className="consent-form-section">
                                {!template ? (
                                    <p className="text-gray-600">Loading form...</p>
                                ) : (
                                    template.content?.questions?.map(renderQuestion)
                                )}
                            </div>

                            {/* Success Message */}
                            {showSuccessMessage && (
                                <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
                                    <div className="flex items-center">
                                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        <span className="font-medium">
                                            ✅ {missingForms.length > 1 ? 'All consent forms' : 'Consent form'} signed successfully!
                                        </span>
                                    </div>
                                    <p className="mt-1 text-sm">
                                        {missingForms.length > 1
                                            ? 'All required forms have been completed and you can now proceed to review your booking.'
                                            : 'Your signature has been saved and you can now proceed to review your booking.'
                                        }
                                    </p>
                                </div>
                            )}

                            <div className="form-actions mt-8 flex justify-between">
                                <button
                                    type="button"
                                    className="px-6 py-2 text-gray-600 hover:text-gray-900 transition-colors"
                                    onClick={handleBack}
                                    disabled={isLoading || showSuccessMessage}
                                >
                                    ← Back
                                </button>
                                <button
                                    type="button"
                                    disabled={
                                        isLoading || showSuccessMessage ||
                                        (!isAlreadySigned && (!agreeToElectronicRecords || !signature.trim()))
                                    }
                                    onClick={handleNext}
                                    className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                >
                                    {showSuccessMessage ? 'Redirecting...' :
                                     isLoading ? 'Processing...' :
                                     (currentFormIndex < missingForms.length - 1) ? 'Next Form' : 'Complete & Review'}
                                </button>
                            </div>
                        </div>

                {/* Order Summary Sidebar - Desktop Only */}
                <div className="hidden lg:block w-80 bg-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>

                    {/* Date and Time */}
                    <div className="mb-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <div className="font-medium text-gray-900">
                                    {bookingData.selectedDate ?
                                        new Date(bookingData.selectedDate).toLocaleDateString('en-US', {
                                            weekday: 'short',
                                            month: 'short',
                                            day: 'numeric',
                                            year: 'numeric'
                                        }) : 'Date not selected'
                                    }
                                </div>
                            </div>
                            <div className="text-right">
                                <div className="font-medium text-gray-900">
                                    {bookingData.selectedTime ?
                                        new Date(`2000-01-01 ${bookingData.selectedTime}`).toLocaleTimeString('en-US', {
                                            hour: 'numeric',
                                            minute: '2-digit',
                                            hour12: true
                                        }) : 'Time not selected'
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Stylist Info */}
                    <div className="mb-6 pb-4 border-b border-gray-300">
                        <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                                <span className="text-sm font-medium text-gray-700">
                                    {(bookingData.selectedEmployee?.display_name || bookingData.selectedEmployee?.name || bookingData.selectedEmployee?.full_name)?.charAt(0) || 'S'}
                                </span>
                            </div>
                            <div className="flex-1">
                                <div className="text-sm text-gray-600">
                                    {bookingData.selectedEmployee?.title || bookingData.selectedEmployee?.role || 'Stylist'}
                                </div>
                                <div className="font-medium text-gray-900">
                                    {bookingData.selectedEmployee?.display_name ||
                                     bookingData.selectedEmployee?.name ||
                                     bookingData.selectedEmployee?.full_name ||
                                     'Stylist'}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Services and Total */}
                    <div className="mb-6">
                        {/* Main Service */}
                        <div className="flex justify-between items-center mb-2">
                            <div className="text-sm text-gray-900">
                                {bookingData.selectedService?.name || 'Classic Lash Fullset(C01-C02)'}
                            </div>
                            <div className="font-medium text-gray-900">
                                ${bookingData.selectedService?.price || '199.00'}
                            </div>
                        </div>

                        {/* Add-ons */}
                        {bookingData.selectedAddOns && bookingData.selectedAddOns.length > 0 && (
                            bookingData.selectedAddOns.map((addon, index) => (
                                <div key={index} className="flex justify-between items-center mb-2">
                                    <div className="text-sm text-gray-900">
                                        {addon.name}
                                    </div>
                                    <div className="font-medium text-gray-900">
                                        ${addon.price}
                                    </div>
                                </div>
                            ))
                        )}

                        {/* Total */}
                        <div className="flex justify-between items-center text-lg font-semibold mt-4">
                            <span>Total</span>
                            <span>${totalCost.toFixed(2)}</span>
                        </div>
                    </div>

                    {/* Payment Summary */}
                    <div className="mb-6 pb-4 border-b border-gray-300 pt-4 border-t border-gray-300">
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm text-gray-900">Total Due Now</span>
                            <span className="font-medium text-gray-900">$0.00</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-900">Total Due at Business</span>
                            <span className="font-medium text-gray-900">${totalCost.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    );
};

export default ConsentFormPage;
